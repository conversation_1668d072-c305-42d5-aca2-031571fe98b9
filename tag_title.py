import os
import base64
import json
import tqdm
import logging
import csv
import time
import os
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import google.generativeai as genai
from PIL import Image
import io

# Configure logging
logging.basicConfig(level=logging.ERROR)

# Configure Gemini API
GOOGLE_API_KEY = "AIzaSyACipMkhTlgdu5kP60hwmTcoVlrRRbYpvs"  # Replace with your actual API key
genai.configure(api_key=GOOGLE_API_KEY)
model = genai.GenerativeModel(model_name="gemini-2.0-flash")

# Configuration settings
MAX_WORKERS = 50  # Number of concurrent workers
BATCH_SIZE = 500  # Number of images to process in each batch
TARGET_SIZE = 2000  # Target size for the longer dimension


def resize_image(image_path, target_size=2000):
    """
    Resize image to target_size pixels on the longer dimension while maintaining aspect ratio
    Returns the resized image as bytes
    """
    try:
        with Image.open(image_path) as img:
            # Convert to RGB if necessary (handles RGBA, P mode, etc.)
            if img.mode not in ('RGB', 'L'):
                img = img.convert('RGB')
            
            # Get current dimensions
            width, height = img.size
            
            # Skip resizing if image is already smaller than target
            if max(width, height) <= target_size:
                # Convert to bytes without resizing
                img_bytes = io.BytesIO()
                img.save(img_bytes, format='JPEG', quality=85, optimize=True)
                return img_bytes.getvalue()
            
            # Calculate new dimensions maintaining aspect ratio
            if width > height:
                # Landscape orientation
                new_width = target_size
                new_height = int((height * target_size) / width)
            else:
                # Portrait orientation
                new_height = target_size
                new_width = int((width * target_size) / height)
            
            # Resize the image
            resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Convert to bytes
            img_bytes = io.BytesIO()
            resized_img.save(img_bytes, format='JPEG', quality=85, optimize=True)
            
            return img_bytes.getvalue()
            
    except Exception as e:
        logging.error(f"Error resizing image {image_path}: {e}")
        return None


def encode_image(image_path):
    try:
        # First resize the image
        resized_image_data = resize_image(image_path, TARGET_SIZE)
        if not resized_image_data:
            return None
            
        # Verify this is a valid image by checking if it starts with JPEG header
        if not resized_image_data.startswith(b'\xff\xd8'):
            logging.error(f"Resized image data for {image_path} doesn't appear to be valid JPEG")
            return None
            
        return base64.b64encode(resized_image_data).decode('utf-8')
        
    except Exception as e:
        logging.error(f"Error encoding image {image_path}: {e}")
        return None


def caption_image(image_path, example_titles=None):
    encoded_image = encode_image(image_path)
    if not encoded_image:
        return None

    json_format = """
    {
      "title": "string", // Create a descriptive title UNDER 200 CHARACTERS that follows these patterns:
                        // 1. Write in complete, natural sentences or descriptive phrases
                        // 2. Include specific details about actions, settings, objects, and context
                        // 3. Use professional, clear language that describes what's happening
                        // 4. Include relevant details about appearance, emotions, activities
                        // 5. Mention setting/location context when relevant
                        // 6. Use present tense and active voice
                        // 7. Be comprehensive but concise - MAXIMUM 200 characters
                        // 8. Avoid brand names or specific product references
      "category": "string", // Choose ONE category that best describes the image only from given categories: Animals (animals/insects/pets), Buildings and Architecture (structures/interiors), Business (office/finance), Drinks (beverages/spirits), The Environment (nature/places), States of Mind (emotions), Food (food/eating), Graphic Resources (backgrounds/textures), Hobbies and Leisure (pastimes/activities), Industry (manufacturing/work), Landscape (vistas/cities/nature), Lifestyle (home/work/play), People (all ages/ethnicities/cultures), Plants and Flowers (natural world), Culture and Religion (traditions/beliefs), Science (applied/natural/medical), Social Issues (poverty/inequality/politics), Sports (fitness/athletics), Technology (computers/devices), Transport (vehicles/systems), Travel (worldwide/culture)
      "tags": ["string"] // Generate exactly 41 single-word keywords in lowercase, ordered by importance (most important first). Consider: 1) Simple words at 1st-6th grade level 2) Highly relevant and commonly searched terms 3) Mix of specific and conceptual terms 4) Feelings/moods/trends 5) NO brand names, trademarks, real people names, or specific locations. Include subject, actions, setting, and emotional concepts.
    }"""

    # Build the prompt based on whether example titles are provided
    if example_titles:
        examples_text = "\n".join([f"- \"{title}\"" for title in example_titles])
        prompt = f"""FIRST: Carefully examine this image and describe exactly what you see - the subjects, actions, objects, setting, emotions, and context.

THEN: Create a title that describes THIS SPECIFIC IMAGE while following the style pattern shown in these examples:

{examples_text}

Study these examples to understand the writing style, but your title must accurately describe what's actually happening in THIS image.

Guidelines:
1. PRIMARY FOCUS: Describe what's actually in the image - people, objects, actions, setting, emotions
2. SECONDARY FOCUS: Use the writing style and structure pattern from the examples
3. Be specific about what you see: clothing, expressions, activities, environment, objects
4. Use the same professional tone and sentence structure as the examples
5. Include relevant details about the actual scene, not generic descriptions
6. CRITICAL: Keep the title under 200 characters - be concise but descriptive

Remember: The examples show you HOW to write, but you must write ABOUT what's actually in this specific image. Keep it under 200 characters.

Provide response in this exact JSON format: {json_format}"""
    else:
        # Use the original prompt if no examples provided
        prompt = f"""Create a title with SHORT DESCRIPTION first, then CONCEPT keywords. KEEP UNDER 200 CHARACTERS.

Study these example titles - notice the pattern:
- "Woman holds hands on stomach highlighting digestive system. Red glowing illustration of human gut, stomach and intestines. Probiotic supplements support gut health and wellness."
- "Biometric security concept, fingerprint scanner technology. Hand touching digital screen, ai, cyber protection, authentication, secure access. Innovation identity defense, data privacy."
- "Young woman live streams selling fashionable clothing. Smiling seller presents details on social media to customers. Girl happy, shows clothes, online retail, selling products at home, social"
- "Mature businessman smiles at camera during office meeting. Confident leader, wearing glasses, poses with arms crossed. Business portrait. Corporate executive at workplace."

Write titles with this structure:
1. START with 1-2 short sentences describing what's happening
2. THEN add conceptual keywords and themes
3. Keep the description BRIEF and DIRECT
4. Follow with relevant concept words buyers search for
5. CRITICAL: Keep total title under 200 characters

Examples of the RIGHT format:
- "Doctor examines patient chart in hospital. Medical consultation, healthcare, professional diagnosis, clinical care"
- "Team discusses project around conference table. Business meeting, corporate collaboration, strategy planning, teamwork"
- "Woman types on laptop at home office. Remote work, digital business, online productivity, work from home"

Pattern: [Short action description] + [Concept keywords] = UNDER 200 CHARACTERS

Keep description SHORT but add the conceptual search terms that stock buyers need.

Provide response in this exact JSON format: {json_format}"""
    
    try:
        response = model.generate_content([
                {"mime_type": "image/jpeg", "data": encoded_image}, prompt
                ])
        raw_response = response.text.strip()

        # Extract JSON object if wrapped in code block
        if raw_response.startswith("```"):
            raw_response = "\n".join(raw_response.split("\n")[1:-1])

        # Validate JSON before returning
        try:
            result = json.loads(raw_response)
            
            # Validate the structure to ensure it has all required fields
            if not all(key in result for key in ['title', 'tags', 'category']):
                logging.error(f"Missing required fields in response for {image_path}")
                return None
                
            # Ensure tags is a list
            if not isinstance(result['tags'], list):
                logging.error(f"Tags is not a list in response for {image_path}")
                return None
                
            # Sanitize text to remove any non-printable characters
            result['title'] = ''.join(c for c in result['title'] if c.isprintable())
            result['category'] = ''.join(c for c in result['category'] if c.isprintable())
            result['tags'] = [(''.join(c for c in tag if c.isprintable())) for tag in result['tags']]
            
            return result
        except json.JSONDecodeError:
            logging.error(f"Invalid JSON response for {image_path}: {raw_response[:100]}...")
            return None

    except Exception as e:
        print(f"Rate limit or other error for {image_path}: {e}")
        return None  # Skip instead of retrying


def process_image(image_path, example_titles=None):
    caption_data = caption_image(image_path, example_titles)
    if caption_data:
        # Clean up data before writing to CSV
        title = caption_data['title'].strip()
        # Join tags with simple commas, no extra spaces
        tags = ','.join([tag.strip() for tag in caption_data['tags']])
        category = caption_data['category'].strip()
        
        return {
            'filename': os.path.basename(image_path),
            'title': title,
            'tags': tags,
            'category': category,
            'path': image_path
        }
    return None


def get_example_titles():
    """Prompt user to paste all example titles at once"""
    print("=" * 60)
    print("TITLE EXAMPLE INPUT")
    print("=" * 60)
    print("Paste all your example titles below (one per line).")
    print("Press Enter twice when finished, or type 'skip' to use default style.")
    print("-" * 60)
    
    try:
        lines = []
        while True:
            line = input().strip()
            if line.lower() == 'skip':
                print("Skipping examples - using default style.")
                return []
            if not line:  # Empty line indicates end of input
                break
            lines.append(line)
        
        # Filter out empty lines
        example_titles = [line for line in lines if line]
        
        # Show what was captured for confirmation
        if example_titles:
            print(f"\n✓ Captured {len(example_titles)} example titles:")
            for i, title in enumerate(example_titles, 1):
                print(f"  {i}. {title[:80]}{'...' if len(title) > 80 else ''}")
            print()
        
        return example_titles
        
    except KeyboardInterrupt:
        print("\nOperation cancelled.")
        return None
    except EOFError:
        return []


def main():
    local_folder = "nt"  # Folder containing images
    output_csv = "FOR_THIS_eps.csv"  # Output CSV file
    
    print(f"Image Processor with Auto-Resize to {TARGET_SIZE}px")
    print("=" * 60)
    print(f"Images will be resized to {TARGET_SIZE}px on the longer side while maintaining aspect ratio.")
    print(f"Smaller images will be left unchanged.")
    print()
    
    # Get example titles from user input
    example_titles = get_example_titles()
    
    if example_titles is None:  # User cancelled
        return
    
    # Display information about examples being used
    print()
    if example_titles:
        print(f"✓ Using {len(example_titles)} example titles to guide AI output:")
        for i, example in enumerate(example_titles, 1):
            print(f"  {i}. {example}")
        print()
        print("The AI will analyze these examples and match their style, tone, and format.")
        print()
    else:
        print("✓ Using default title generation style.")
        print()
    
    # Validate folder exists
    if not os.path.exists(local_folder):
        print(f"Error: Folder '{local_folder}' does not exist.")
        return
    
    # Create CSV file with headers
    with open(output_csv, 'w', newline='', encoding='utf-8') as csv_file:
        csv_writer = csv.writer(csv_file, dialect='excel', quoting=csv.QUOTE_ALL)
        csv_writer.writerow(['Filename', 'Title', 'Keywords', 'Category'])

    image_paths = [
        os.path.join(local_folder, filename)
        for filename in os.listdir(local_folder)
        if filename.lower().endswith((".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"))
    ]
    
    if not image_paths:
        print(f"No image files found in folder '{local_folder}'.")
        return
    
    print(f"Found {len(image_paths)} images to process.")
    print(f"Images will be resized to max {TARGET_SIZE}px maintaining aspect ratio.")
    print(f"Output will be saved to: {output_csv}")
    print(f"Using {MAX_WORKERS} workers with batch size {BATCH_SIZE}")
    print()

    # Thread lock for CSV writing
    csv_lock = threading.Lock()
    
    def write_to_csv(result_data):
        with csv_lock:
            with open(output_csv, 'a', newline='', encoding='utf-8') as csv_file:
                csv_writer = csv.writer(csv_file, dialect='excel', quoting=csv.QUOTE_ALL)
                csv_writer.writerow([
                    result_data['filename'],
                    result_data['title'],
                    result_data['tags'],
                    result_data['category']
                ])

    # Process images in batches
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # Create chunks of futures using BATCH_SIZE
        for i in range(0, len(image_paths), BATCH_SIZE):
            chunk = image_paths[i:i + BATCH_SIZE]
            futures = {
                executor.submit(process_image, image_path, example_titles): image_path 
                for image_path in chunk
            }
            
            for future in tqdm.tqdm(
                as_completed(futures), 
                total=len(chunk), 
                desc=f'Batch {i//BATCH_SIZE + 1}/{len(image_paths)//BATCH_SIZE + 1}', 
                ncols=100
            ):
                image_path = futures[future]
                try:
                    result = future.result()
                    if result:
                        write_to_csv(result)
                        os.remove(result['path'])
                        print(f"Processed and deleted: {result['filename']}")
                    else:
                        print(f"Failed to caption: {os.path.basename(image_path)}")
                except Exception as e:
                    logging.error(f"Error processing {image_path}: {e}")

    print(f"Processing complete. Results saved to {output_csv}")


if __name__ == "__main__":
    main()