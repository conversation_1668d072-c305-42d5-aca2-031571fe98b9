import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk
import os
import threading

class BatchPNGToJPEGConverter:
    def __init__(self, root):
        self.root = root
        self.root.title("Batch PNG to JPEG Converter")
        self.root.geometry("600x500")
        
        # Variables
        self.input_folder = None
        self.output_folder = None
        self.png_files = []
        
        # Create GUI elements
        self.create_widgets()
        
    def create_widgets(self):
        # Title
        title_label = tk.Label(self.root, text="Batch PNG to JPEG Converter", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Input folder frame
        input_frame = tk.LabelFrame(self.root, text="Input Folder (PNG files)", 
                                   font=("Arial", 10, "bold"))
        input_frame.pack(pady=10, padx=20, fill="x")
        
        input_select_frame = tk.Frame(input_frame)
        input_select_frame.pack(fill="x", pady=10, padx=10)
        
        self.input_label = tk.Label(input_select_frame, text="No folder selected", 
                                   relief="sunken", anchor="w", bg="white")
        self.input_label.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        input_btn = tk.Button(input_select_frame, text="Browse", 
                             command=self.select_input_folder, width=12)
        input_btn.pack(side="right")
        
        # Output folder frame
        output_frame = tk.LabelFrame(self.root, text="Output Folder (JPEG files)", 
                                    font=("Arial", 10, "bold"))
        output_frame.pack(pady=10, padx=20, fill="x")
        
        output_select_frame = tk.Frame(output_frame)
        output_select_frame.pack(fill="x", pady=10, padx=10)
        
        self.output_label = tk.Label(output_select_frame, text="No folder selected", 
                                    relief="sunken", anchor="w", bg="white")
        self.output_label.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        output_btn = tk.Button(output_select_frame, text="Browse", 
                              command=self.select_output_folder, width=12)
        output_btn.pack(side="right")
        
        # Files info frame
        info_frame = tk.LabelFrame(self.root, text="Files Information", 
                                  font=("Arial", 10, "bold"))
        info_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        # Files list with scrollbar
        list_frame = tk.Frame(info_frame)
        list_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        scrollbar = tk.Scrollbar(list_frame)
        scrollbar.pack(side="right", fill="y")
        
        self.files_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set)
        self.files_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.config(command=self.files_listbox.yview)
        
        # Files count label
        self.count_label = tk.Label(info_frame, text="0 PNG files found", 
                                   font=("Arial", 10))
        self.count_label.pack(pady=5)
        
        # Progress frame
        progress_frame = tk.Frame(self.root)
        progress_frame.pack(pady=10, padx=20, fill="x")
        
        self.progress_var = tk.StringVar()
        self.progress_var.set("Ready to convert")
        progress_label = tk.Label(progress_frame, textvariable=self.progress_var)
        progress_label.pack()
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill="x", pady=5)
        
        # Convert button
        self.convert_btn = tk.Button(self.root, text="Start Batch Conversion", 
                                    command=self.start_conversion, bg="#4CAF50", 
                                    fg="white", font=("Arial", 12, "bold"), height=2)
        self.convert_btn.pack(pady=20)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = tk.Label(self.root, textvariable=self.status_var, 
                             relief="sunken", anchor="w")
        status_bar.pack(side="bottom", fill="x")
    
    def select_input_folder(self):
        folder_path = filedialog.askdirectory(title="Select Input Folder (containing PNG files)")
        
        if folder_path:
            self.input_folder = folder_path
            self.input_label.config(text=folder_path)
            self.scan_png_files()
            self.status_var.set(f"Input folder selected: {os.path.basename(folder_path)}")
    
    def select_output_folder(self):
        folder_path = filedialog.askdirectory(title="Select Output Folder (for JPEG files)")
        
        if folder_path:
            self.output_folder = folder_path
            self.output_label.config(text=folder_path)
            self.status_var.set(f"Output folder selected: {os.path.basename(folder_path)}")
    
    def scan_png_files(self):
        if not self.input_folder:
            return
        
        self.png_files = []
        self.files_listbox.delete(0, tk.END)
        
        try:
            for filename in os.listdir(self.input_folder):
                if filename.lower().endswith('.png'):
                    self.png_files.append(filename)
                    self.files_listbox.insert(tk.END, filename)
            
            count = len(self.png_files)
            self.count_label.config(text=f"{count} PNG files found")
            
            if count == 0:
                messagebox.showinfo("Info", "No PNG files found in the selected folder.")
            
        except Exception as e:
            messagebox.showerror("Error", f"Could not scan folder:\n{str(e)}")
    
    def start_conversion(self):
        if not self.input_folder:
            messagebox.showwarning("Warning", "Please select an input folder.")
            return
        
        if not self.output_folder:
            messagebox.showwarning("Warning", "Please select an output folder.")
            return
        
        if not self.png_files:
            messagebox.showwarning("Warning", "No PNG files found in the input folder.")
            return
        
        # Start conversion in a separate thread to prevent GUI freezing
        self.convert_btn.config(state="disabled", text="Converting...")
        threading.Thread(target=self.convert_images, daemon=True).start()
    
    def convert_images(self):
        total_files = len(self.png_files)
        successful = 0
        failed = 0
        
        # Reset progress bar
        self.progress_bar.config(maximum=total_files, value=0)
        
        for i, filename in enumerate(self.png_files):
            try:
                # Update progress
                self.root.after(0, lambda i=i, f=filename: self.update_progress(i, f, total_files))
                
                input_path = os.path.join(self.input_folder, filename)
                output_filename = os.path.splitext(filename)[0] + '.jpg'
                output_path = os.path.join(self.output_folder, output_filename)
                
                # Open the PNG image
                image = Image.open(input_path)
                
                # Create a white background
                if image.mode in ('RGBA', 'LA'):
                    # Create white background
                    background = Image.new('RGB', image.size, 'white')
                    
                    # Paste the image on white background
                    if image.mode == 'RGBA':
                        background.paste(image, (0, 0), image)
                    else:  # LA mode
                        background.paste(image, (0, 0))
                    
                    result_image = background
                else:
                    # Convert to RGB if not already
                    result_image = image.convert('RGB')
                
                # Save as JPEG
                result_image.save(output_path, 'JPEG', quality=95)
                successful += 1
                
            except Exception as e:
                print(f"Error converting {filename}: {str(e)}")
                failed += 1
        
        # Update GUI when conversion is complete
        self.root.after(0, lambda: self.conversion_complete(successful, failed))
    
    def update_progress(self, current, filename, total):
        self.progress_bar.config(value=current + 1)
        self.progress_var.set(f"Converting: {filename} ({current + 1}/{total})")
        self.status_var.set(f"Processing: {filename}")
    
    def conversion_complete(self, successful, failed):
        self.convert_btn.config(state="normal", text="Start Batch Conversion")
        self.progress_var.set("Conversion completed")
        
        if failed == 0:
            self.status_var.set(f"All {successful} files converted successfully!")
            messagebox.showinfo("Success", 
                               f"Batch conversion completed!\n\n"
                               f"Successfully converted: {successful} files\n"
                               f"Files saved to: {self.output_folder}")
        else:
            self.status_var.set(f"Completed with {failed} errors")
            messagebox.showwarning("Completed with Errors", 
                                  f"Batch conversion completed with some errors:\n\n"
                                  f"Successfully converted: {successful} files\n"
                                  f"Failed: {failed} files\n"
                                  f"Files saved to: {self.output_folder}")

def main():
    root = tk.Tk()
    app = BatchPNGToJPEGConverter(root)
    root.mainloop()

if __name__ == "__main__":
    main()