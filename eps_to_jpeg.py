import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor
from PIL import Image
import queue
import time
import subprocess
import sys
import shutil
from datetime import datetime

class EPSToJPEGConverter:
    def __init__(self, root):
        self.root = root
        self.root.title("EPS to JPEG Batch Converter")
        self.root.geometry("700x700")

        self.files = []
        self.output_dir = ""
        self.is_converting = False
        self.executor = None
        self.error_log = []

        # Queue for thread-safe GUI updates
        self.update_queue = queue.Queue()

        # Check dependencies on startup
        self.check_dependencies()

        self.setup_ui()
        self.check_queue()

    def check_dependencies(self):
        """Check if required dependencies are installed"""
        errors = []

        # Check Pillow
        try:
            import PIL
            self.log_message(f"✓ Pillow {PIL.__version__} is installed")
        except ImportError:
            error_msg = "✗ Pillow is not installed. Install with: pip install Pillow"
            errors.append(error_msg)
            self.log_message(error_msg)

        # Check Ghostscript
        if self.check_ghostscript():
            self.log_message("✓ Ghostscript is available")
        else:
            error_msg = "✗ Ghostscript not found. Install from: https://www.ghostscript.com/download/gsdnld.html"
            errors.append(error_msg)
            self.log_message(error_msg)

        if errors:
            messagebox.showwarning(
                "Dependencies Missing",
                "Some dependencies are missing:\n\n" + "\n".join(errors) +
                "\n\nThe application may not work correctly for EPS files."
            )

    def check_ghostscript(self):
        """Check if Ghostscript is installed and accessible"""
        try:
            # Try common Ghostscript command names
            gs_commands = ['gs', 'gswin64c', 'gswin32c', 'ghostscript']

            for cmd in gs_commands:
                try:
                    result = subprocess.run([cmd, '--version'],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        return True
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    continue

            return False
        except Exception:
            return False

    def log_message(self, message):
        """Add a message to the error log with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.error_log.append(log_entry)

        # Update log display if it exists
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, log_entry + "\n")
            self.log_text.see(tk.END)

    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # File selection
        ttk.Label(main_frame, text="Select EPS Files:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        file_frame = ttk.Frame(main_frame)
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(file_frame, text="Browse Files", command=self.browse_files).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(file_frame, text="Clear Files", command=self.clear_files).grid(row=0, column=1)
        
        # File list
        self.file_listbox = tk.Listbox(main_frame, height=8)
        self.file_listbox.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Scrollbar for listbox
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.file_listbox.yview)
        scrollbar.grid(row=2, column=2, sticky=(tk.N, tk.S))
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        # Output directory
        ttk.Label(main_frame, text="Output Directory:").grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        
        output_frame = ttk.Frame(main_frame)
        output_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.output_var = tk.StringVar()
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_var, width=50)
        self.output_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(output_frame, text="Browse", command=self.browse_output).grid(row=0, column=1)
        
        # Worker settings
        settings_frame = ttk.LabelFrame(main_frame, text="Conversion Settings", padding="5")
        settings_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(settings_frame, text="Number of Workers:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.workers_var = tk.IntVar(value=4)
        workers_spinbox = ttk.Spinbox(settings_frame, from_=1, to=16, textvariable=self.workers_var, width=10)
        workers_spinbox.grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(settings_frame, text="JPEG Quality:").grid(row=0, column=2, sticky=tk.W, padx=(10, 5))
        self.quality_var = tk.IntVar(value=95)
        quality_spinbox = ttk.Spinbox(settings_frame, from_=1, to=100, textvariable=self.quality_var, width=10)
        quality_spinbox.grid(row=0, column=3, sticky=tk.W)
        
        # Progress
        ttk.Label(main_frame, text="Progress:").grid(row=6, column=0, sticky=tk.W, pady=(0, 5))
        
        self.progress = ttk.Progressbar(main_frame, mode='determinate')
        self.progress.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.status_var = tk.StringVar(value="Ready")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var)
        self.status_label.grid(row=8, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        
        # Convert button
        self.convert_btn = ttk.Button(main_frame, text="Start Conversion", command=self.start_conversion)
        self.convert_btn.grid(row=9, column=0, pady=(0, 5))
        
        self.stop_btn = ttk.Button(main_frame, text="Stop", command=self.stop_conversion, state='disabled')
        self.stop_btn.grid(row=9, column=1, pady=(0, 5))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        file_frame.columnconfigure(0, weight=1)
        output_frame.columnconfigure(0, weight=1)
    
    def browse_files(self):
        files = filedialog.askopenfilenames(
            title="Select EPS Files",
            filetypes=[("EPS files", "*.eps"), ("All files", "*.*")]
        )
        
        if files:
            self.files.extend(files)
            self.update_file_list()
    
    def clear_files(self):
        self.files.clear()
        self.update_file_list()
    
    def update_file_list(self):
        self.file_listbox.delete(0, tk.END)
        for file in self.files:
            self.file_listbox.insert(tk.END, os.path.basename(file))
    
    def browse_output(self):
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir = directory
            self.output_var.set(directory)
    
    def convert_file(self, file_path, output_dir, quality):
        """Convert a single EPS file to JPEG"""
        try:
            filename = os.path.splitext(os.path.basename(file_path))[0]
            output_path = os.path.join(output_dir, f"{filename}.jpg")
            
            # Open and convert EPS to JPEG
            with Image.open(file_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Save as JPEG
                img.save(output_path, 'JPEG', quality=quality, optimize=True)
            
            return True, f"Converted: {filename}.eps"
        
        except Exception as e:
            return False, f"Error converting {os.path.basename(file_path)}: {str(e)}"
    
    def conversion_worker(self, files_chunk, output_dir, quality):
        """Worker function for converting files"""
        for file_path in files_chunk:
            if not self.is_converting:
                break
            
            success, message = self.convert_file(file_path, output_dir, quality)
            self.update_queue.put(('progress', success, message))
    
    def start_conversion(self):
        if not self.files:
            messagebox.showwarning("Warning", "Please select EPS files to convert.")
            return
        
        if not self.output_dir:
            messagebox.showwarning("Warning", "Please select an output directory.")
            return
        
        if not os.path.exists(self.output_dir):
            messagebox.showerror("Error", "Output directory does not exist.")
            return
        
        # Reset progress
        self.progress['maximum'] = len(self.files)
        self.progress['value'] = 0
        self.is_converting = True
        self.convert_btn['state'] = 'disabled'
        self.stop_btn['state'] = 'normal'
        
        # Start conversion in separate thread
        self.conversion_thread = threading.Thread(target=self.run_conversion)
        self.conversion_thread.daemon = True
        self.conversion_thread.start()
    
    def run_conversion(self):
        """Run the conversion process with multiple workers"""
        try:
            num_workers = self.workers_var.get()
            quality = self.quality_var.get()
            
            self.update_queue.put(('status', f"Starting conversion with {num_workers} workers..."))
            
            # Split files among workers
            chunk_size = max(1, len(self.files) // num_workers)
            file_chunks = [self.files[i:i + chunk_size] for i in range(0, len(self.files), chunk_size)]
            
            # Use ThreadPoolExecutor for better control
            with ThreadPoolExecutor(max_workers=num_workers) as executor:
                self.executor = executor
                
                # Submit all chunks
                futures = []
                for chunk in file_chunks:
                    if chunk:  # Only submit non-empty chunks
                        future = executor.submit(self.conversion_worker, chunk, self.output_dir, quality)
                        futures.append(future)
                
                # Wait for all workers to complete
                for future in futures:
                    if not self.is_converting:
                        break
                    future.result()
            
            if self.is_converting:
                self.update_queue.put(('complete', 'Conversion completed successfully!'))
            else:
                self.update_queue.put(('complete', 'Conversion stopped by user.'))
                
        except Exception as e:
            self.update_queue.put(('error', f"Conversion error: {str(e)}"))
        
        finally:
            self.executor = None
    
    def stop_conversion(self):
        """Stop the conversion process"""
        self.is_converting = False
        if self.executor:
            self.executor.shutdown(wait=False)
        
        self.convert_btn['state'] = 'normal'
        self.stop_btn['state'] = 'disabled'
        self.status_var.set("Stopping conversion...")
    
    def check_queue(self):
        """Check for updates from worker threads"""
        try:
            while True:
                msg_type, *args = self.update_queue.get_nowait()
                
                if msg_type == 'progress':
                    success, message = args
                    self.progress['value'] += 1
                    self.status_var.set(message)
                    
                elif msg_type == 'status':
                    self.status_var.set(args[0])
                    
                elif msg_type == 'complete':
                    self.status_var.set(args[0])
                    self.convert_btn['state'] = 'normal'
                    self.stop_btn['state'] = 'disabled'
                    self.is_converting = False
                    
                elif msg_type == 'error':
                    messagebox.showerror("Error", args[0])
                    self.convert_btn['state'] = 'normal'
                    self.stop_btn['state'] = 'disabled'
                    self.is_converting = False
                    
        except queue.Empty:
            pass
        
        # Schedule next check
        self.root.after(100, self.check_queue)

def main():
    root = tk.Tk()
    app = EPSToJPEGConverter(root)
    root.mainloop()

if __name__ == "__main__":
    main()